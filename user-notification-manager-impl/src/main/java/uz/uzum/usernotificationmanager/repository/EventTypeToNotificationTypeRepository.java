package uz.uzum.usernotificationmanager.repository;

import java.util.Optional;
import java.util.UUID;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import uz.uzum.usernotificationmanager.model.enums.EventType;
import uz.uzum.usernotificationmanager.model.enums.NotificationType;
import uz.uzum.usernotificationmanager.model.persistent.EventTypeToNotificationTypeEntity;

@Repository
public interface EventTypeToNotificationTypeRepository extends CrudRepository<EventTypeToNotificationTypeEntity, UUID> {
    Optional<NotificationType> findNotificationTypeByEventType(EventType eventType);
}
