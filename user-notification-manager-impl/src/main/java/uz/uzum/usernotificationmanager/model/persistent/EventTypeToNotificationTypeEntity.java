package uz.uzum.usernotificationmanager.model.persistent;

import java.util.UUID;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;
import uz.uzum.usernotificationmanager.model.enums.EventType;
import uz.uzum.usernotificationmanager.model.enums.NotificationType;

@Table(name = "event_type_to_notification_type")
public record EventTypeToNotificationTypeEntity(
        @Id UUID uuid, EventType eventType, NotificationType notificationType) {}
