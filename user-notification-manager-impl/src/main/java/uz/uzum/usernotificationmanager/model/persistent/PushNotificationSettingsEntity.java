package uz.uzum.usernotificationmanager.model.persistent;

import java.util.Map;
import java.util.UUID;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;
import uz.uzum.usernotificationmanager.model.enums.EventType;

@Table(name = "push_notification_settings")
public record PushNotificationSettingsEntity(
        @Id UUID uuid,
        EventType eventType,
        Boolean isSilent,
        String titleTemplate,
        String bodyTemplate,
        Map<String, String> data,
        String imageUrl) {}
