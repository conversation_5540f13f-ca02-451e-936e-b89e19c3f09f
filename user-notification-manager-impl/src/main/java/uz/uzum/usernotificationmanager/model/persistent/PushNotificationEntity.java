package uz.uzum.usernotificationmanager.model.persistent;

import java.util.Map;
import java.util.UUID;
import org.springframework.data.annotation.Id;
import org.springframework.data.relational.core.mapping.Table;
import uz.uzum.usernotificationmanager.model.enums.EventType;
import uz.uzum.usernotificationmanager.model.enums.PushNotificationStatus;

@Table(name = "push_notification")
public record PushNotificationEntity(
        @Id UUID uuid,
        String fcmToken,
        PushNotificationStatus status,
        EventType eventType,
        Boolean isSilent,
        String title,
        String body,
        Map<String, String> data,
        String imageUrl) {}
